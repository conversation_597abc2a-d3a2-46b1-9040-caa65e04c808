#!/usr/bin/env python3
"""
NEUROGLYPH Expanded Dataset Audit Test
Verifica che il dataset espanso rispetti tutti i Principi Immutabili.

PRINCIPI IMMUTABILI NON NEGOZIABILI:
1. Atomicità: 1 simbolo = 1 token = 1 concetto
2. Unicità Unicode: nessun duplicato di codepoint
3. Reversibilità: AST ↔ NEUROGLYPH senza perdita (≥95%)
4. Semantica: mapping preciso a significato matematico/logico
5. Scientifico: riproducibilità + certificazione + audit trail
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Set, Any
from collections import Counter
from difflib import SequenceMatcher

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from neuroglyph.core.constants import (
        AUDIT_FIDELITY_THRESHOLD,
        AUDIT_SUCCESS_RATE_REQUIRED,
        AUDIT_REJECTION_RATE_MIN
    )
except ImportError:
    # Fallback values if constants not available
    AUDIT_FIDELITY_THRESHOLD = 0.95
    AUDIT_SUCCESS_RATE_REQUIRED = 0.95
    AUDIT_REJECTION_RATE_MIN = 0.95

class ExpandedDatasetAuditor:
    """Auditor per dataset espanso con standard immutabili."""
    
    def __init__(self, dataset_path: str = "data/neuroglyph_certified_v2_expanded.json"):
        self.dataset_path = dataset_path
        self.min_fidelity = AUDIT_FIDELITY_THRESHOLD
        self.min_success_rate = AUDIT_SUCCESS_RATE_REQUIRED
        
        # Unicode symbols used in NEUROGLYPH (expanded set)
        self.neuroglyph_symbols = {
            '∀', '∃', '∧', '∨', '¬', '⇒', '⇔', '⊢', '⊨',  # Logic
            '∪', '∩', '⊆', '⊇', '∈', '∉', '∅', '⊂', '⊃',  # Sets
            '=', '≠', '≤', '≥', '<', '>', '±', '∞', '∑', '∏',  # Math
            'ℝ', 'ℕ', 'ℤ', 'ℚ', 'ℂ', '𝔹',  # Domains
            '∫', '∂', 'ᵢ', '\\', '/', 'φ', 'ψ', 'χ'  # Extended math symbols
        }
    
    def load_dataset(self) -> Dict[str, Any]:
        """Load and validate dataset structure."""
        print(f"📚 Loading dataset: {self.dataset_path}")
        
        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ Dataset loaded successfully")
            print(f"   Total patterns: {len(data.get('patterns', []))}")
            print(f"   Metadata: {data.get('metadata', {}).get('name', 'Unknown')}")
            
            return data
        
        except FileNotFoundError:
            print(f"❌ Dataset not found: {self.dataset_path}")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON: {e}")
            return {}
    
    def test_principle_1_atomicity(self, patterns: List[Dict]) -> Dict[str, Any]:
        """Test Principle 1: Atomicity - 1 symbol = 1 token = 1 concept."""
        print("\n🔬 TESTING PRINCIPLE 1: ATOMICITY")
        print("=" * 50)
        
        atomic_violations = []
        symbol_usage = Counter()
        
        for i, pattern in enumerate(patterns):
            input_text = pattern.get('input', '')
            output_text = pattern.get('output', '')
            
            # Count symbol usage
            for symbol in self.neuroglyph_symbols:
                symbol_usage[symbol] += input_text.count(symbol) + output_text.count(symbol)
            
            # Check for multi-character symbols (violation of atomicity)
            if '::' in input_text or '::' in output_text:
                atomic_violations.append({
                    'pattern_id': i,
                    'input': input_text,
                    'output': output_text,
                    'violation': 'Multi-character symbol detected'
                })
        
        atomicity_score = 1.0 - (len(atomic_violations) / len(patterns))
        
        print(f"   Atomic violations: {len(atomic_violations)}")
        print(f"   Atomicity score: {atomicity_score:.3f}")
        print(f"   Symbol usage diversity: {len([s for s, c in symbol_usage.items() if c > 0])}")
        
        return {
            'principle': 'Atomicity',
            'score': atomicity_score,
            'violations': atomic_violations,
            'symbol_usage': dict(symbol_usage),
            'passed': atomicity_score >= self.min_fidelity
        }
    
    def test_principle_2_unicode_uniqueness(self, patterns: List[Dict]) -> Dict[str, Any]:
        """Test Principle 2: Unicode Uniqueness - no duplicate codepoints."""
        print("\n🔬 TESTING PRINCIPLE 2: UNICODE UNIQUENESS")
        print("=" * 50)
        
        all_symbols = set()
        duplicate_violations = []
        
        for pattern in patterns:
            input_text = pattern.get('input', '')
            output_text = pattern.get('output', '')
            
            # Extract all Unicode symbols
            for char in input_text + output_text:
                if ord(char) > 127:  # Non-ASCII Unicode
                    if char in all_symbols and char in self.neuroglyph_symbols:
                        # This is expected - symbols should be reused
                        pass
                    all_symbols.add(char)
        
        # Check for unexpected duplicates (symbols not in registry)
        unexpected_symbols = all_symbols - self.neuroglyph_symbols
        
        uniqueness_score = 1.0 - (len(unexpected_symbols) / max(len(all_symbols), 1))
        
        print(f"   Total Unicode symbols: {len(all_symbols)}")
        print(f"   NEUROGLYPH symbols: {len(all_symbols & self.neuroglyph_symbols)}")
        print(f"   Unexpected symbols: {len(unexpected_symbols)}")
        if unexpected_symbols:
            print(f"   Unexpected symbols list: {list(unexpected_symbols)}")
        print(f"   Uniqueness score: {uniqueness_score:.3f}")
        
        return {
            'principle': 'Unicode Uniqueness',
            'score': uniqueness_score,
            'total_symbols': len(all_symbols),
            'neuroglyph_symbols': len(all_symbols & self.neuroglyph_symbols),
            'unexpected_symbols': list(unexpected_symbols),
            'passed': uniqueness_score >= self.min_fidelity
        }
    
    def test_principle_3_reversibility(self, patterns: List[Dict]) -> Dict[str, Any]:
        """Test Principle 3: Reversibility - AST ↔ NEUROGLYPH without loss."""
        print("\n🔬 TESTING PRINCIPLE 3: REVERSIBILITY")
        print("=" * 50)
        
        perfect_matches = 0
        high_fidelity_matches = 0
        fidelity_scores = []
        
        for pattern in patterns:
            input_text = pattern.get('input', '')
            output_text = pattern.get('output', '')
            expected_fidelity = pattern.get('fidelity', 1.0)
            
            # Calculate actual fidelity
            if input_text.strip() == output_text.strip():
                actual_fidelity = 1.0
                perfect_matches += 1
            else:
                actual_fidelity = SequenceMatcher(None, input_text, output_text).ratio()
            
            fidelity_scores.append(actual_fidelity)
            
            if actual_fidelity >= self.min_fidelity:
                high_fidelity_matches += 1
        
        avg_fidelity = sum(fidelity_scores) / len(fidelity_scores) if fidelity_scores else 0
        perfect_rate = perfect_matches / len(patterns)
        success_rate = high_fidelity_matches / len(patterns)
        
        print(f"   Perfect matches: {perfect_matches}/{len(patterns)} ({perfect_rate:.1%})")
        print(f"   High fidelity (≥{self.min_fidelity:.1%}): {high_fidelity_matches}/{len(patterns)} ({success_rate:.1%})")
        print(f"   Average fidelity: {avg_fidelity:.3f}")
        
        return {
            'principle': 'Reversibility',
            'score': avg_fidelity,  # Add missing score field
            'perfect_matches': perfect_matches,
            'high_fidelity_matches': high_fidelity_matches,
            'perfect_rate': perfect_rate,
            'success_rate': success_rate,
            'avg_fidelity': avg_fidelity,
            'fidelity_scores': fidelity_scores,
            'passed': success_rate >= self.min_success_rate and avg_fidelity >= self.min_fidelity
        }
    
    def test_principle_4_semantics(self, patterns: List[Dict]) -> Dict[str, Any]:
        """Test Principle 4: Semantic precision to mathematical/logical meaning."""
        print("\n🔬 TESTING PRINCIPLE 4: SEMANTIC PRECISION")
        print("=" * 50)
        
        semantic_categories = {
            'logical_reasoning': 0,
            'set_theory': 0,
            'mathematical_reasoning': 0,
            'complex_reasoning': 0,
            'unknown': 0
        }
        
        valid_ast_types = {
            'UniversalQuantification', 'ExistentialQuantification', 'MixedQuantification',
            'MaterialImplication', 'LogicalConjunction', 'LogicalDisjunction', 
            'LogicalNegation', 'LogicalEquivalence', 'BinaryOperation', 
            'FunctionCall', 'ComplexLogical', 'ComplexSet', 'ComplexMath'
        }
        
        semantic_violations = []
        
        for i, pattern in enumerate(patterns):
            category = pattern.get('category', 'unknown')
            ast_type = pattern.get('ast_type', 'unknown')
            
            if category in semantic_categories:
                semantic_categories[category] += 1
            else:
                semantic_categories['unknown'] += 1
            
            # Check AST type validity
            if ast_type not in valid_ast_types:
                semantic_violations.append({
                    'pattern_id': i,
                    'category': category,
                    'ast_type': ast_type,
                    'violation': 'Invalid AST type'
                })
        
        semantic_score = 1.0 - (len(semantic_violations) / len(patterns))
        coverage_score = len([c for c in semantic_categories.values() if c > 0]) / len(semantic_categories)
        
        print(f"   Semantic violations: {len(semantic_violations)}")
        print(f"   Semantic score: {semantic_score:.3f}")
        print(f"   Category coverage: {coverage_score:.3f}")
        print(f"   Categories: {semantic_categories}")
        
        return {
            'principle': 'Semantic Precision',
            'score': semantic_score,
            'coverage_score': coverage_score,
            'categories': semantic_categories,
            'violations': semantic_violations,
            'passed': semantic_score >= self.min_fidelity and coverage_score >= 0.8
        }
    
    def test_principle_5_scientific(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test Principle 5: Scientific reproducibility + certification + audit trail."""
        print("\n🔬 TESTING PRINCIPLE 5: SCIENTIFIC REPRODUCIBILITY")
        print("=" * 50)
        
        metadata = data.get('metadata', {})
        
        required_fields = [
            'certification_date', 'total_patterns', 'validated_patterns',
            'validation_rate', 'min_fidelity_required', 'audit_lock_verified'
        ]
        
        missing_fields = [field for field in required_fields if field not in metadata]
        
        # Check audit trail completeness
        audit_score = 1.0 - (len(missing_fields) / len(required_fields))
        
        # Check reproducibility markers
        has_expansion_info = 'expansion_info' in metadata
        has_certification = metadata.get('audit_lock_verified', False)
        validation_rate = metadata.get('validation_rate', 0)
        
        scientific_score = (audit_score + float(has_expansion_info) + float(has_certification)) / 3
        
        print(f"   Missing metadata fields: {len(missing_fields)}")
        print(f"   Audit score: {audit_score:.3f}")
        print(f"   Has expansion info: {has_expansion_info}")
        print(f"   Has certification: {has_certification}")
        print(f"   Validation rate: {validation_rate:.1%}")
        print(f"   Scientific score: {scientific_score:.3f}")
        
        return {
            'principle': 'Scientific Reproducibility',
            'score': scientific_score,
            'audit_score': audit_score,
            'missing_fields': missing_fields,
            'has_expansion_info': has_expansion_info,
            'has_certification': has_certification,
            'validation_rate': validation_rate,
            'passed': scientific_score >= self.min_fidelity and validation_rate >= self.min_success_rate
        }
    
    def run_complete_audit(self) -> Dict[str, Any]:
        """Run complete audit of all 5 Immutable Principles."""
        print("🔒 NEUROGLYPH EXPANDED DATASET AUDIT")
        print("=" * 80)
        print("Testing compliance with all 5 Immutable Principles...")
        print("=" * 80)
        
        # Load dataset
        data = self.load_dataset()
        if not data:
            return {'audit_passed': False, 'error': 'Failed to load dataset'}
        
        patterns = data.get('patterns', [])
        if not patterns:
            return {'audit_passed': False, 'error': 'No patterns found in dataset'}
        
        # Run all principle tests
        results = {}
        
        results['principle_1'] = self.test_principle_1_atomicity(patterns)
        results['principle_2'] = self.test_principle_2_unicode_uniqueness(patterns)
        results['principle_3'] = self.test_principle_3_reversibility(patterns)
        results['principle_4'] = self.test_principle_4_semantics(patterns)
        results['principle_5'] = self.test_principle_5_scientific(data)
        
        # Calculate overall audit result
        all_passed = all(result.get('passed', False) for result in results.values())
        overall_score = sum(result.get('score', 0) for result in results.values()) / len(results)
        
        print(f"\n🎯 AUDIT RESULTS SUMMARY")
        print("=" * 80)
        
        for i, (key, result) in enumerate(results.items(), 1):
            status = "✅ PASSED" if result.get('passed', False) else "❌ FAILED"
            principle_name = result.get('principle', 'Unknown')
            score = result.get('score', 0)
            print(f"   Principle {i} ({principle_name}): {status} (Score: {score:.3f})")
        
        print(f"\n📊 OVERALL AUDIT:")
        print(f"   Overall score: {overall_score:.3f}")
        print(f"   All principles passed: {all_passed}")
        print(f"   Audit status: {'✅ PASSED' if all_passed else '❌ FAILED'}")
        
        return {
            'audit_passed': all_passed,
            'overall_score': overall_score,
            'total_patterns': len(patterns),
            'results': results,
            'dataset_path': self.dataset_path
        }

def main():
    """Main audit runner."""
    auditor = ExpandedDatasetAuditor()
    audit_result = auditor.run_complete_audit()
    
    if audit_result['audit_passed']:
        print(f"\n🎉 AUDIT PASSED!")
        print(f"✅ Expanded dataset complies with all 5 Immutable Principles")
        print(f"📊 Overall score: {audit_result['overall_score']:.3f}")
        return True
    else:
        print(f"\n❌ AUDIT FAILED!")
        print(f"🚨 Dataset violates one or more Immutable Principles")
        print(f"📊 Overall score: {audit_result['overall_score']:.3f}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
