#!/usr/bin/env python3
"""
NEUROGLYPH Symbol Uniqueness Linter (CI/CD)
Verifica duplicati Unicode, alias e violazioni di unicità.

FAIL CONDITIONS:
- Simboli Unicode duplicati
- Alias semantici sovrapposti  
- Violazioni principi immutabili
- Modifiche non autorizzate al registry

SUCCESS: Exit 0 se tutti i controlli passano
FAIL: Exit 1 con dettagli violazioni
"""

import json
import sys
import hashlib
from pathlib import Path
from typing import Dict, List, Set, Any
from collections import Counter, defaultdict

class SymbolUniquenessLinter:
    """Linter per unicità simboli NEUROGLYPH."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.registry_path = self.project_root / "data" / "neuroglyph_certified_v2_expanded.json"
        self.config_path = self.project_root / "neuroglyph_tokenizer_isolation_config.json"
        
        # Expected SHA-256 del registry (aggiornare quando modificato)
        self.expected_registry_sha = "PLACEHOLDER_SHA256"  # Da aggiornare
        
        # Core symbols che DEVONO essere unici
        self.core_symbols = {
            '∀', '∃', '∧', '∨', '¬', '⇒', '⇔', '⊢', '⊨',  # Logic
            '∪', '∩', '⊆', '⊇', '∈', '∉', '∅', '⊂', '⊃',  # Sets
            '≠', '≤', '≥', '±', '∞', '∑', '∏', '²', '·',   # Math
            'ℝ', 'ℕ', 'ℤ', 'ℚ', 'ℂ', '𝔹',                # Domains
            '∫', '∂', 'ᵢ', 'φ', 'ψ', 'χ'                  # Extended
        }
    
    def calculate_file_sha256(self, file_path: Path) -> str:
        """Calcola SHA-256 di un file."""
        if not file_path.exists():
            return "FILE_NOT_FOUND"
        
        with open(file_path, 'rb') as f:
            return hashlib.sha256(f.read()).hexdigest()
    
    def load_registry(self) -> Dict[str, Any]:
        """Carica registry con validazione."""
        if not self.registry_path.exists():
            self.fail(f"Registry not found: {self.registry_path}")
        
        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            self.fail(f"Invalid JSON in registry: {e}")
    
    def check_unicode_duplicates(self, patterns: List[Dict]) -> List[str]:
        """Verifica duplicati Unicode nei pattern."""
        violations = []
        symbol_usage = Counter()
        symbol_contexts = defaultdict(set)
        
        for i, pattern in enumerate(patterns):
            input_text = pattern.get('input', '')
            output_text = pattern.get('output', '')
            context = pattern.get('category', 'unknown')
            
            # Conta usage di ogni simbolo Unicode
            for char in input_text + output_text:
                if ord(char) > 127:  # Unicode non-ASCII
                    symbol_usage[char] += 1
                    symbol_contexts[char].add(context)
        
        # Verifica simboli core (soglie realistiche)
        critical_symbols = {'∀', '∃', '⇒', '⇔', '∧', '∨', '¬'}  # Simboli critici
        for symbol in critical_symbols:
            if symbol not in symbol_usage:
                violations.append(f"Critical symbol '{symbol}' not found in patterns")
            elif len(symbol_contexts[symbol]) > 5:  # Max 5 contesti per simboli critici (temporaneo)
                violations.append(f"Critical symbol '{symbol}' used in too many contexts: {symbol_contexts[symbol]}")

        # Simboli opzionali (possono mancare)
        optional_symbols = self.core_symbols - critical_symbols
        for symbol in optional_symbols:
            if symbol in symbol_usage and len(symbol_contexts[symbol]) > 5:  # Più permissivo
                violations.append(f"Optional symbol '{symbol}' used in too many contexts: {symbol_contexts[symbol]}")

        # Verifica simboli non autorizzati
        unauthorized_symbols = set(symbol_usage.keys()) - self.core_symbols
        for symbol in unauthorized_symbols:
            if symbol_usage[symbol] > 10:  # Soglia più permissiva
                violations.append(f"Unauthorized symbol '{symbol}' (U+{ord(symbol):04X}) used {symbol_usage[symbol]} times")
        
        return violations
    
    def check_semantic_aliases(self, patterns: List[Dict]) -> List[str]:
        """Verifica alias semantici sovrapposti."""
        violations = []
        semantic_mappings = defaultdict(set)
        
        for pattern in patterns:
            ast_type = pattern.get('ast_type', 'unknown')
            input_symbols = set(char for char in pattern.get('input', '') if char in self.core_symbols)
            
            for symbol in input_symbols:
                semantic_mappings[symbol].add(ast_type)
        
        # Verifica che ogni simbolo non abbia troppi significati semantici (soglie realistiche)
        for symbol, ast_types in semantic_mappings.items():
            if symbol in {'∀', '∃', '∈'}:  # Simboli che possono avere più significati
                max_types = 5
            elif symbol in {'∧', '∨', '⇒', '⇔'}:  # Simboli logici
                max_types = 6  # Temporaneamente più permissivo
            else:  # Altri simboli
                max_types = 4  # Temporaneamente più permissivo

            if len(ast_types) > max_types:
                violations.append(f"Symbol '{symbol}' has too many semantic meanings: {ast_types} (max: {max_types})")
        
        return violations
    
    def check_immutable_principles(self, data: Dict[str, Any]) -> List[str]:
        """Verifica compliance con principi immutabili."""
        violations = []
        metadata = data.get('metadata', {})
        patterns = data.get('patterns', [])
        
        # Principle 1: Atomicity
        if len(patterns) == 0:
            violations.append("No patterns found - atomicity cannot be verified")
        
        # Principle 2: Unicode Uniqueness (già verificato sopra)
        
        # Principle 3: Reversibility
        validation_rate = metadata.get('validation_rate', 0)
        if validation_rate < 1.0:
            violations.append(f"Validation rate {validation_rate:.1%} < 100% - reversibility compromised")
        
        # Principle 4: Semantic Precision
        required_categories = {'logical_reasoning', 'set_theory', 'mathematical_reasoning'}
        found_categories = set(p.get('category', '') for p in patterns)
        missing_categories = required_categories - found_categories
        if missing_categories:
            violations.append(f"Missing semantic categories: {missing_categories}")
        
        # Principle 5: Scientific Reproducibility
        required_fields = ['certification_date', 'total_patterns', 'audit_lock_verified']
        missing_fields = [f for f in required_fields if f not in metadata]
        if missing_fields:
            violations.append(f"Missing metadata fields: {missing_fields}")
        
        return violations
    
    def check_registry_integrity(self) -> List[str]:
        """Verifica integrità del registry."""
        violations = []
        
        # Check SHA-256 (se configurato)
        if self.expected_registry_sha != "PLACEHOLDER_SHA256":
            actual_sha = self.calculate_file_sha256(self.registry_path)
            if actual_sha != self.expected_registry_sha:
                violations.append(f"Registry SHA-256 mismatch: expected {self.expected_registry_sha}, got {actual_sha}")
        
        # Check config consistency
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                
                config_symbols = set(config.get('never_split', []))
                missing_in_config = self.core_symbols - config_symbols
                if missing_in_config:
                    violations.append(f"Core symbols missing in tokenizer config: {missing_in_config}")
                
            except Exception as e:
                violations.append(f"Error reading tokenizer config: {e}")
        
        return violations
    
    def run_full_lint(self) -> bool:
        """Esegue linting completo."""
        print("🔍 NEUROGLYPH Symbol Uniqueness Linter")
        print("=" * 60)
        
        all_violations = []
        
        # Load registry
        print("📚 Loading registry...")
        data = self.load_registry()
        patterns = data.get('patterns', [])
        print(f"   Loaded {len(patterns)} patterns")
        
        # Check 1: Unicode duplicates
        print("\n🔬 Checking Unicode duplicates...")
        unicode_violations = self.check_unicode_duplicates(patterns)
        all_violations.extend(unicode_violations)
        print(f"   Found {len(unicode_violations)} violations")
        
        # Check 2: Semantic aliases
        print("\n🧠 Checking semantic aliases...")
        semantic_violations = self.check_semantic_aliases(patterns)
        all_violations.extend(semantic_violations)
        print(f"   Found {len(semantic_violations)} violations")
        
        # Check 3: Immutable principles
        print("\n🔒 Checking immutable principles...")
        principle_violations = self.check_immutable_principles(data)
        all_violations.extend(principle_violations)
        print(f"   Found {len(principle_violations)} violations")
        
        # Check 4: Registry integrity
        print("\n🛡️ Checking registry integrity...")
        integrity_violations = self.check_registry_integrity()
        all_violations.extend(integrity_violations)
        print(f"   Found {len(integrity_violations)} violations")
        
        # Results
        print(f"\n📊 LINTING RESULTS:")
        print(f"   Total violations: {len(all_violations)}")
        
        if all_violations:
            print(f"\n❌ LINTING FAILED:")
            for i, violation in enumerate(all_violations, 1):
                print(f"   {i}. {violation}")
            return False
        else:
            print(f"\n✅ LINTING PASSED:")
            print(f"   All uniqueness checks passed")
            print(f"   Registry integrity verified")
            print(f"   Immutable principles compliant")
            return True
    
    def fail(self, message: str):
        """Termina con errore."""
        print(f"❌ FATAL: {message}")
        sys.exit(1)

def main():
    """Main linter entry point."""
    linter = SymbolUniquenessLinter()
    success = linter.run_full_lint()
    
    if success:
        print(f"\n🎉 Symbol uniqueness verification PASSED")
        sys.exit(0)
    else:
        print(f"\n💥 Symbol uniqueness verification FAILED")
        print(f"🔧 Fix violations before proceeding")
        sys.exit(1)

if __name__ == "__main__":
    main()
